<?php

namespace Tests\Unit\Controller;

use App\Http\Controllers\ReservationController;
use App\Models\Field;
use App\Models\Utility;
use App\Services\FieldAvailabilityService;
use App\Services\ReservationCostService;
use App\Services\ReservationValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(ReservationController::class)]
class ReservationControllerTest extends TestCase
{
    use RefreshDatabase;

    protected ReservationController $controller;

    protected Field $field;

    protected Utility $utility;

    protected FieldAvailabilityService $availabilityService;

    protected ReservationCostService $costService;

    protected ReservationValidationService $validationService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test field
        $this->field = Field::factory()->active()->create([
            'hourly_rate' => 50.00,
            'min_booking_hours' => 1,
            'max_booking_hours' => 4,
        ]);

        // Create test utility
        $this->utility = Utility::factory()->active()->create([
            'hourly_rate' => 10.00,
        ]);

        // Mock services
        $this->availabilityService = Mockery::mock(FieldAvailabilityService::class);
        $this->costService = Mockery::mock(ReservationCostService::class);
        $this->validationService = Mockery::mock(ReservationValidationService::class);

        // Create controller with mocked services
        $this->controller = new ReservationController(
            $this->availabilityService,
            $this->costService,
            $this->validationService
        );
    }

    #[Test]
    public function create_returns_view_with_fields_and_utilities()
    {
        // Arrange
        Field::factory()->active()->count(2)->create();
        Utility::factory()->active()->count(2)->create();

        // Mock the availability service call
        $this->availabilityService->shouldReceive('getFieldAvailabilityCalendar')
            ->andReturn([]);

        $request = new Request;

        // Act
        $response = $this->controller->create($request);

        // Assert
        $this->assertEquals('reservations.create', $response->name());
        $this->assertArrayHasKey('fields', $response->getData());
        $this->assertArrayHasKey('utilities', $response->getData());

        $fields = $response->getData()['fields'];
        $utilities = $response->getData()['utilities'];

        $this->assertCount(3, $fields); // 2 + 1 from setUp
        $this->assertCount(3, $utilities); // 2 + 1 from setUp
    }

    #[Test]
    public function create_pre_fills_form_when_field_id_provided()
    {
        // Arrange
        $this->availabilityService->shouldReceive('getFieldAvailabilityCalendar')
            ->andReturn([]);

        $request = new Request(['field_id' => $this->field->id]);

        // Act
        $response = $this->controller->create($request);

        // Assert
        $this->assertEquals('reservations.create', $response->name());
        $this->assertEquals($this->field->id, $response->getData()['selectedField']->id);
    }

    // ========================================
    // CHECK AVAILABILITY UNIT TESTS
    // ========================================

    #[Test]
    public function check_availability_returns_json_response_with_slots()
    {
        // Arrange
        $this->availabilityService->shouldReceive('getAvailableTimeSlots')
            ->with(\Mockery::type(Field::class), '2024-12-25', 2.0, null)
            ->andReturn([
                ['start' => '10:00', 'end' => '12:00'],
                ['start' => '14:00', 'end' => '16:00'],
            ]);

        $request = new Request([
            'field_id' => $this->field->id,
            'date' => '2024-12-25',
            'duration_hours' => 2,
        ]);

        // Act
        $response = $this->controller->checkAvailability($request);

        // Assert
        $this->assertInstanceOf(\Illuminate\Http\JsonResponse::class, $response);
        $data = $response->getData(true);
        $this->assertTrue($data['available']);
        $this->assertEquals('2 time slots available', $data['message']);
        $this->assertCount(2, $data['slots']);
    }

    #[Test]
    public function check_availability_returns_no_slots_when_unavailable()
    {
        // Arrange
        $this->availabilityService->shouldReceive('getAvailableTimeSlots')
            ->with(\Mockery::type(Field::class), '2024-12-25', 2.0, null)
            ->andReturn([]);

        $request = new Request([
            'field_id' => $this->field->id,
            'date' => '2024-12-25',
            'duration_hours' => 2,
        ]);

        // Act
        $response = $this->controller->checkAvailability($request);

        // Assert
        $this->assertInstanceOf(\Illuminate\Http\JsonResponse::class, $response);
        $data = $response->getData(true);
        $this->assertFalse($data['available']);
        $this->assertEquals('No available time slots', $data['message']);
        $this->assertEmpty($data['slots']);
    }

    #[Test]
    public function check_availability_validates_input_and_returns_error()
    {
        // Arrange
        $request = new Request([
            'field_id' => 999, // Non-existent field
            'date' => 'invalid-date',
            'duration_hours' => 'invalid',
        ]);

        // Act
        $response = $this->controller->checkAvailability($request);

        // Assert
        $this->assertInstanceOf(\Illuminate\Http\JsonResponse::class, $response);
        $data = $response->getData(true);
        $this->assertFalse($data['available']);
        $this->assertEquals('Invalid input', $data['message']);
        $this->assertEmpty($data['slots']);
    }

    #[Test]
    public function check_availability_excludes_specified_reservation()
    {
        // Arrange
        $this->availabilityService->shouldReceive('getAvailableTimeSlots')
            ->with(\Mockery::type(Field::class), '2024-12-25', 2.0, 123)
            ->andReturn([
                ['start' => '10:00', 'end' => '12:00'],
            ]);

        $request = new Request([
            'field_id' => $this->field->id,
            'date' => '2024-12-25',
            'duration_hours' => 2,
            'exclude_reservation_id' => 123,
        ]);

        // Act
        $response = $this->controller->checkAvailability($request);

        // Assert
        $this->assertInstanceOf(\Illuminate\Http\JsonResponse::class, $response);
        $data = $response->getData(true);
        $this->assertTrue($data['available']);
        $this->assertCount(1, $data['slots']);
    }

    // ========================================
    // GET AVAILABLE END TIMES UNIT TESTS
    // ========================================

    #[Test]
    public function get_available_end_times_returns_valid_end_times()
    {
        // Arrange
        $this->availabilityService->shouldReceive('isFieldAvailable')
            ->andReturn(true);

        $request = new Request([
            'field_id' => $this->field->id,
            'date' => '2024-12-25',
            'start_time' => '10:00',
        ]);

        // Act
        $response = $this->controller->getAvailableEndTimes($request);

        // Assert
        $this->assertInstanceOf(\Illuminate\Http\JsonResponse::class, $response);
        $data = $response->getData(true);
        $this->assertTrue($data['success']);
        $this->assertArrayHasKey('end_times', $data);
        $this->assertArrayHasKey('message', $data);
    }

    #[Test]
    public function get_available_end_times_validates_input_and_returns_error()
    {
        // Arrange
        $request = new Request([
            'field_id' => 999, // Non-existent field
            'date' => 'invalid-date',
            'start_time' => 'invalid-time',
        ]);

        // Act
        $response = $this->controller->getAvailableEndTimes($request);

        // Assert
        $this->assertInstanceOf(\Illuminate\Http\JsonResponse::class, $response);
        $data = $response->getData(true);
        $this->assertFalse($data['success']);
        $this->assertEquals('Invalid input', $data['message']);
        $this->assertEmpty($data['end_times']);
    }

    #[Test]
    public function get_available_end_times_excludes_specified_reservation()
    {
        // Arrange
        $this->availabilityService->shouldReceive('isFieldAvailable')
            ->andReturn(true);

        $request = new Request([
            'field_id' => $this->field->id,
            'date' => '2024-12-25',
            'start_time' => '10:00',
            'exclude_reservation_id' => 123,
        ]);

        // Act
        $response = $this->controller->getAvailableEndTimes($request);

        // Assert
        $this->assertInstanceOf(\Illuminate\Http\JsonResponse::class, $response);
        $data = $response->getData(true);
        $this->assertTrue($data['success']);
        $this->assertArrayHasKey('end_times', $data);
    }

    // ========================================
    // GET COST ESTIMATE UNIT TESTS
    // ========================================

    #[Test]
    public function get_cost_estimate_returns_valid_estimate()
    {
        // Arrange
        $expectedEstimate = [
            'field_name' => 'Test Field',
            'hourly_rate' => 50.00,
            'duration_hours' => 2.0,
            'total_cost' => 120.00,
            'field_cost' => 100.00,
            'utility_cost' => 20.00,
            'utility_breakdown' => [
                ['name' => 'Test Utility', 'hours' => 2, 'rate' => 10.00, 'cost' => 20.00],
            ],
            'formatted' => [
                'total_cost' => '$120.00',
                'field_cost' => '$100.00',
                'utility_cost' => '$20.00',
            ],
            'peak_hour_info' => null,
        ];

        $this->costService->shouldReceive('getReservationEstimate')
            ->with($this->field->id, 2.0, '10:00', [['id' => $this->utility->id, 'hours' => 2]])
            ->andReturn($expectedEstimate);

        $request = new Request([
            'field_id' => $this->field->id,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'utilities' => [
                ['id' => $this->utility->id, 'hours' => 2],
            ],
        ]);

        // Act
        $response = $this->controller->getCostEstimate($request);

        // Assert
        $this->assertInstanceOf(\Illuminate\Http\JsonResponse::class, $response);
        $data = $response->getData(true);
        $this->assertEquals($expectedEstimate, $data);
    }

    #[Test]
    public function get_cost_estimate_validates_input_and_returns_error()
    {
        // Arrange
        $request = new Request([
            'field_id' => 999, // Non-existent field
            'start_time' => 'invalid-time',
            'end_time' => 'invalid-time',
        ]);

        // Act
        $response = $this->controller->getCostEstimate($request);

        // Assert
        $this->assertInstanceOf(\Illuminate\Http\JsonResponse::class, $response);
        $data = $response->getData(true);
        $this->assertArrayHasKey('error', $data);
        $this->assertEquals('Invalid input', $data['error']);
        $this->assertArrayHasKey('validation_errors', $data);
    }

    #[Test]
    public function get_cost_estimate_validates_end_time_after_start_time()
    {
        // Arrange
        $request = new Request([
            'field_id' => $this->field->id,
            'start_time' => '12:00',
            'end_time' => '10:00', // End time before start time
        ]);

        // Act
        $response = $this->controller->getCostEstimate($request);

        // Assert
        $this->assertInstanceOf(\Illuminate\Http\JsonResponse::class, $response);
        $data = $response->getData(true);
        $this->assertArrayHasKey('error', $data);
        $this->assertEquals('End time must be after start time', $data['error']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
